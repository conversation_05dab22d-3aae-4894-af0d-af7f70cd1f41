{"name": "components", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@motionone/utils": "^10.18.0", "@react-three/fiber": "^9.3.0", "@tsparticles/engine": "^3.9.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.9.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "lenis": "^1.3.8", "lucide-react": "^0.536.0", "motion-number": "^1.0.0", "next": "15.4.5", "npm": "^11.5.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "three": "^0.179.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.179.0", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}