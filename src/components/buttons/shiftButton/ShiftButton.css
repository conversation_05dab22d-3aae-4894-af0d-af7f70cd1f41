.shift-button [data-button-animate-chars] span {
    display: inline-block;
    position: relative;
    text-shadow: 0px calc(var(--text-sm)*1.5) currentColor;
    transform: translateY(0em) rotate(0.001deg);
    transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.shift-button:hover [data-button-animate-chars] span {
    transform: translateY(calc(var(--text-sm) * -1.5)) rotate(0.001deg);
}

.shift-button:hover .shift-button-bg {
    transform: scale(0.975);
}

@media (max-width: 768px) {
    .shift-button [data-button-animate-chars] span {
        text-shadow: 0px calc(var(--text-sm)*1.5) currentColor;
    }

    .shift-button:hover [data-button-animate-chars] span {
        transform: translateY(0vw) rotate(0);
    }

    .shift-button:hover .shift-button-bg {
        transform: scale(1);
    }
}