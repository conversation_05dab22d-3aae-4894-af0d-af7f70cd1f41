import { TokenomicsStyle } from "../types";
import { futuristicTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFuturisticColors } from "../../shared/themeConfig";

export function getFuturisticTokenomicsStyle(
  colorTemplate: ColorTemplate = 1
): TokenomicsStyle {
  const colors = getFuturisticColors(colorTemplate);

  return {
    section: {
      className: theme.spacing.sectionPadding,
      backgroundColor: colors.primary,
      spotlight: {
        width: "25%",
        height: "150%",
        left: "0%",
        top: "-30%",
        rotate: "-60deg",
        color: colors.spotlight,
        blur: "100px",
        opacity: 1,
        mobileWidth: "55%",
        mobileHeight: "70%",
        mobileLeft: "-10%",
        mobileTop: "-60%",
        mobileRotate: "-30deg",
      },
    },
    title: {
      className: `${theme.heading.sizes.hero} leading-none tracking-tight ${theme.text.headingClass} ${theme.heading.className}`,
      animation: "slide",
      useRetroText: false,
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
      gradientColors: theme.gradients.text,
    },
    description: {
      className: `mt-4 ${theme.description.className} max-w-3xl text-off-white`,
    },
    bento: {
      items: [],
      className: "",
      gridClassName: "gap-3 md:gap-6",
      itemClassName: `${colors.cardBg} futuristic-card-border gap-16! md:gap-20!`,
      valueClassName: `${theme.tokenomics.value.large} font-semibold ${theme.heading.className}`,
      gradientColors: theme.gradients.text,
      descriptionClassName: `${theme.tokenomics.description.small} font-medium ${theme.description.className} text-off-white`,
    },
    expandingCards: {
      cardClassName:
        "relative md:h-90 w-full flex flex-col rounded-[var(--border-radius)] overflow-hidden p-8 md:p-10 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] cursor-pointer select-none",
      activeCardClassName: `${colors.cardBg} futuristic-card-border opacity-100`,
      inactiveCardClassName: `${colors.cardBg} futuristic-card-border opacity-60 hover:opacity-90`,
      numberClassName:
        "text-sm font-medium text-white/50 uppercase tracking-wide",
      titleClassName: `${theme.tokenomics.value.large} font-semibold ${theme.heading.className} text-white`,
      descriptionClassName: `text-off-white pb-7 md:pb-0 md:mt-2 md:text-lg text-base`,
      buttonType: "slide",
      buttonClassName:
        "bg-white/10 border !border-white/20 !rounded-full size-10 p-0 flex items-center justify-center",
      buttonIconClassName: "md:size-8 size-6 text-white",
    },
  };
}

export const futuristicTokenomicsStyle = getFuturisticTokenomicsStyle(1);
