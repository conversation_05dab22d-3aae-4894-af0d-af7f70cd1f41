.mask {
  -webkit-mask-image: linear-gradient(
    90deg,
    transparent var(--i, 0%),
    black var(--i, 0%),
    black var(--f, 100%),
    transparent var(--f, 100%)
  );
  mask-image: linear-gradient(
    90deg,
    transparent var(--i, 0%),
    black var(--i, 0%),
    black var(--f, 100%),
    transparent var(--f, 100%)
  );
}

.ease-expanding {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.gradient-text {
  background: linear-gradient(
    180deg,
    var(--gradient-from) 0%,
    var(--gradient-to) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Mobile accordion transitions */
@media (max-width: 767px) {
  .expanding-card-mobile {
    transition: height 0.7s cubic-bezier(0.4, 0, 0.2, 1),
      background-color 0.3s ease, border-color 0.3s ease;
  }

  .expanding-card-mobile.collapsed {
    overflow: hidden;
  }

  .expanding-card-mobile.expanded {
    overflow: visible;
  }
}
