'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import TechyHero from '@/components/sections/layouts/hero/TechyHero'
import { PageNav } from '@/components/common/PageNav'
import { getTechyFuturisticHeroStyle } from '@/components/sections/styles/hero/techy/futuristicAndOutOfBox'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FuturisticHeroContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getTechyFuturisticHeroStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <TechyHero style={heroStyle} />
    </ReactLenis>
  )
}

export default function FuturisticTechyHeroPage() {
  return (
    <Suspense>
      <FuturisticHeroContent />
    </Suspense>
  )
}
