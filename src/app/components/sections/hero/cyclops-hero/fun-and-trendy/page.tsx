"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import CyclopsHero from "@/components/sections/layouts/hero/CyclopsHero";
import { PageNav } from "@/components/common/PageNav";
import { getRetroCyclopsHeroStyle } from "@/components/sections/styles/hero/cyclops/funAndTrendy";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function RetroCyclopsHeroContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get("theme") as unknown as ColorTemplate) || 1;
  const heroStyle = getRetroCyclopsHeroStyle(Number(theme) as ColorTemplate);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <CyclopsHero style={heroStyle} />
    </ReactLenis>
  );
}

export default function FunAndTrendyCyclopsHeroPage() {
  return (
    <Suspense>
      <RetroCyclopsHeroContent />
    </Suspense>
  );
}
