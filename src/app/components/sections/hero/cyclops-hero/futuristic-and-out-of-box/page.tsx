'use client';

import React, { Suspense } from 'react';
import { ReactLenis } from 'lenis/react';
import CyclopsHero from '@/components/sections/layouts/hero/CyclopsHero';
import { PageNav } from '@/components/common/PageNav';
import { getFuturisticCyclopsHeroStyle } from '@/components/sections/styles/hero/cyclops/futuristicAndOutOfBox';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig';

function FuturisticCyclopsHeroContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1;
  const heroStyle = getFuturisticCyclopsHeroStyle(Number(theme) as ColorTemplate);
  
  const handleBuyClick = () => {
    console.log('Buy button clicked');
    // Add your buy logic here
  };

  const handleJoinCultClick = () => {
    console.log('Join the Cult button clicked');
    // Add your join cult logic here
  };

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <CyclopsHero 
        style={heroStyle} 
        onPrimaryButtonClick={handleBuyClick}
        onSecondaryButtonClick={handleJoinCultClick}
      />
    </ReactLenis>
  );
}

export default function FuturisticCyclopsHeroPage() {
  return (
    <Suspense>
      <FuturisticCyclopsHeroContent />
    </Suspense>
  );
}
