'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import PlayfulHero from '@/components/sections/layouts/hero/PlayfulHero'
import { PageNav } from '@/components/common/PageNav'
import { getFuturisticHeroStyle } from '@/components/sections/styles/hero/playful/futuristicAndOutOfBox'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FuturisticAndOutOfBoxContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticHeroStyle(theme)
  
  return <PlayfulHero style={style} />
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense>
        <FuturisticAndOutOfBoxContent />
      </Suspense>
    </ReactLenis>
  )
}