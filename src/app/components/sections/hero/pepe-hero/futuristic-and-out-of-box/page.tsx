"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import PepeHero from "@/components/sections/layouts/hero/PepeHero";
import { PageNav } from "@/components/common/PageNav";
import { getFuturisticPepeHeroStyle } from "@/components/sections/styles/hero/pepe/futuristicAndOutOfBox";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FuturisticPepeHeroContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const heroStyle = getFuturisticPepeHeroStyle(theme);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PepeHero style={heroStyle} />
    </ReactLenis>
  );
}

export default function FuturisticPepePage() {
  return (
    <Suspense>
      <FuturisticPepeHeroContent />
    </Suspense>
  );
}
