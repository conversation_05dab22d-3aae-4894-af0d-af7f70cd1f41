'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react';
import { PageNav } from '@/components/common/PageNav'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import SimpleHero from '@/components/sections/layouts/hero/SimpleHero';
import { getFunAndTrendyHeroStyle } from '@/components/sections/styles/hero/simple/funAndTrendy';

function FunAndTrendyHeroContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendyHeroStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SimpleHero style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendyHeroContent />
    </Suspense>
  )
}