'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import SimpleHero from '@/components/sections/layouts/hero/SimpleHero'
import { getFuturisticHeroStyle } from '@/components/sections/styles/hero/simple/futuristicAndOutOfBox'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FuturisticSimpleHeroContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticHeroStyle(theme)
  
  return (
    <SimpleHero 
      style={style}
      title="The People's Cryptocurrency"
      description="FLOKI is the utility token of the Floki ecosystem."
      primaryButtonText="Learn More"
      secondaryButtonText="Explore"
    />
  )
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense>
        <FuturisticSimpleHeroContent />
      </Suspense>
    </ReactLenis>
  )
}