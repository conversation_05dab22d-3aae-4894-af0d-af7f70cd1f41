'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import PlayfulHero from '@/components/sections/layouts/hero/PlayfulHero'
import PlayfulAbout from '@/components/sections/layouts/about/PlayfulAbout'
import HowToBuy3D from '@/components/sections/layouts/howtobuy/HowToBuy3D'
import Tokenomics from '@/components/sections/layouts/tokenomics/PlayfulTokenomics'
import { PageNav } from '@/components/common/PageNav'
import { getFunAndTrendyHeroStyle } from '@/components/sections/styles/hero/playful/funAndTrendy'
import { getFunAndTrendyAboutStyle } from '@/components/sections/styles/about/standard/funAndTrendy'
import { getFunAndTrendyHowToBuyStyle } from '@/components/sections/styles/howtobuy/how-to-buy-3d/funAndTrendy'
import { getFunAndTrendyTokenomicsStyle } from '@/components/sections/styles/tokenomics/pudgy/funAndTrendy'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FunAndTrendyContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const colorTemplate = Number(theme) as ColorTemplate
  
  const heroStyle = getFunAndTrendyHeroStyle(colorTemplate)
  const aboutStyle = getFunAndTrendyAboutStyle(colorTemplate)
  const howToBuyStyle = getFunAndTrendyHowToBuyStyle(colorTemplate)
  const tokenomicsStyle = getFunAndTrendyTokenomicsStyle(colorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PlayfulHero style={heroStyle} />
      <PlayfulAbout style={aboutStyle} />
      <HowToBuy3D style={howToBuyStyle} />
      <Tokenomics style={tokenomicsStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendyContent />
    </Suspense>
  )
}