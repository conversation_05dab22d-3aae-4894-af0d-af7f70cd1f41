'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import HowToBuy3D from '@/components/sections/layouts/howtobuy/HowToBuy3D'
import { PageNav } from '@/components/common/PageNav'
import { getFunAndTrendyHowToBuyStyle } from '@/components/sections/styles/howtobuy/how-to-buy-3d/funAndTrendy'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FunAndTrendyContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFunAndTrendyHowToBuyStyle(theme)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <HowToBuy3D style={style} />
    </ReactLenis>
  )
}

export default function FunAndTrendyHowToBuyPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyContent />
    </Suspense>
  )
}