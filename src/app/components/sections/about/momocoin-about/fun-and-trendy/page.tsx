"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { getFunAndTrendyAboutMomocoinStyle } from "@/components/sections/styles/about/momocoin/funAndTrendy";
import Momocoinabout from "@/components/sections/layouts/about/Momocoinabout";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";

function FunAndTrendyAboutContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get("theme") as unknown as ColorTemplate) || 1;
  const aboutStyle = getFunAndTrendyAboutMomocoinStyle(
    Number(theme) as ColorTemplate
  );

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Momocoinabout style={aboutStyle} />
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FunAndTrendyAboutContent />
    </Suspense>
  );
}
