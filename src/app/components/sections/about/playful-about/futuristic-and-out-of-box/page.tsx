'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import PlayfulAbout from '@/components/sections/layouts/about/PlayfulAbout'
import { PageNav } from '@/components/common/PageNav'
import { futuristicAndOutOfBoxAboutStyle } from '@/components/sections/styles/about/standard/futuristicAndOutOfBox'

export default function FuturisticAndOutOfBoxAboutPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PlayfulAbout style={futuristicAndOutOfBoxAboutStyle} />
    </ReactLenis>
  )
}