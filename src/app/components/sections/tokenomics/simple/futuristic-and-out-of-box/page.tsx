'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { getFuturisticTokenomicsStyle } from '@/components/sections/styles/tokenomics/simple/futuristicAndOutOfBox'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import SimpleTokenomics from '@/components/sections/layouts/tokenomics/SimpleTokenomics'

function FuturisticTokenomics() {
    const searchParams = useSearchParams()
    const theme = searchParams.get('theme')
    const style = getFuturisticTokenomicsStyle(theme === '2' ? 2 : 1)

    return (
        <ReactLenis root>
            <PageNav position="bottom" />
            <SimpleTokenomics style={style} />
        </ReactLenis>
    )
}

export default function FuturisticTokenomicsPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <FuturisticTokenomics />
        </Suspense>
    )
}
