'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { getFunAndTrendyTokenomicsStyle } from '@/components/sections/styles/tokenomics/simple/funAndTrendy'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import SimpleTokenomics from '@/components/sections/layouts/tokenomics/SimpleTokenomics'

function FunAndTrendyTokenomics() {
  const searchParams = useSearchParams()
  const theme = searchParams.get('theme')
  const style = getFunAndTrendyTokenomicsStyle(theme === '2' ? 2 : 1)

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SimpleTokenomics style={style} />
    </ReactLenis>
  )
}

export default function FunAndTrendyTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTokenomics />
    </Suspense>
  )
}
