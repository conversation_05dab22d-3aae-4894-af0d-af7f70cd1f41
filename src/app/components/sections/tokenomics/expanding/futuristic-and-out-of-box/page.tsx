"use client";

import React from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { getFuturisticTokenomicsStyle } from "@/components/sections/styles/tokenomics/expanding/futuristicAndOutOfBox";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import ExpandingTokenomics from "@/components/sections/layouts/tokenomics/ExpandingTokenomics";

function FuturisticTokenomics() {
  const searchParams = useSearchParams();
  const theme = searchParams.get("theme");
  const style = getFuturisticTokenomicsStyle(theme === "2" ? 2 : 1);

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <ExpandingTokenomics style={style} />
    </ReactLenis>
  );
}

export default function FuturisticTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticTokenomics />
    </Suspense>
  );
}
