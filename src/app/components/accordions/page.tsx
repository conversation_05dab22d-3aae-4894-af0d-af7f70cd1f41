'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const accordions = [
  { name: 'Regular Accordion', href: '/components/accordions/regular-accordion' },
  { name: 'Image Accordion', href: '/components/accordions/image-accordion' },
  { name: 'Plain Accordion', href: '/components/accordions/plain-accordion' },
  { name: 'Side Accordion', href: '/components/accordions/side-accordion' },
  { name: 'Bento Accordion', href: '/components/accordions/bento-accordion' }
];

export default function AccordionsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {accordions.map((accordion) => (
            <Link key={accordion.name} href={accordion.href}
            >
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{accordion.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}