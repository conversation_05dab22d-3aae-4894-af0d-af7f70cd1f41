'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import KPIBento from '@/components/bento/KPIBento'
import { PageNav } from '@/components/common/PageNav'
import { DollarSign, Users, Briefcase } from 'lucide-react'

const kpiItems = [
  { value: '100', description: 'million', longDescription: 'dollars in client revenue driven by our tailored solutions and strategies.', icon: DollarSign },
  { value: '700', description: 'thousand', longDescription: 'unique visitors engaging with the websites we build every month.', icon: Users },
  { value: '250', description: 'projects', longDescription: 'successfully delivered across multiple industries.', icon: Briefcase }
]

export default function KPIBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <section className="min-h-screen flex items-center px-[var(--width-10)]">
        <KPIBento items={kpiItems} />
      </section>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">End</p>
      </div>
    </ReactLenis>
  )
}