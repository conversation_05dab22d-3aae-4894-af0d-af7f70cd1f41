'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import SimpleKPIBento from '@/components/bento/SimpleKPIBento'
import { PageNav } from '@/components/common/PageNav'

const fourItems = [
  { value: '98%', description: 'Customer Satisfaction Rate Measured Across All Our Products and Services' },
  { value: '2.5M+', description: 'Active Monthly Users Engaging With Our Platform and Mobile Applications' },
  { value: '45ms', description: 'Average API Response Time Measured Globally Across All Data Centers' },
  { value: '99.9%', description: 'Service Uptime Guarantee Maintained Throughout the Year for Enterprise Clients' },
]

export default function SimpleKPIBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <section className="min-h-screen flex items-center px-[var(--width-10)]">
        <SimpleKPIBento items={fourItems} valueClassName='text-6xl' descriptionClassName='text-base' />
      </section>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">End</p>
      </div>
    </ReactLenis>
  )
}