'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import GalleryBento, { type GalleryItem } from '@/components/bento/galleryBento/GalleryBento'
import { PageNav } from '@/components/common/PageNav'

const galleryItems: GalleryItem[] = [
  {
    title: '6345 Dwayne Ave',
    image: '/images/placeholder1.avif'
  },
  {
    title: '2870 Via Bellota',
    image: '/images/placeholder4.avif'
  },
  {
    title: '1204 Blue Sky Dr',
    image: '/images/placeholder2.avif'
  },
  {
    title: '5342 Chelsea St',
    image: '/images/placeholder3.avif'
  }
]

export default function GalleryBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <GalleryBento items={galleryItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}