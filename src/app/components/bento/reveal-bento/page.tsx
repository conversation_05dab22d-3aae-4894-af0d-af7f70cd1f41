'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import RevealBento, { type RevealBentoItem } from '@/components/bento/revealBento/RevealBento'
import { PageNav } from '@/components/common/PageNav'
import { Code2, Palette, Database, Globe, Shield } from 'lucide-react'

const revealItems: RevealBentoItem[] = [
  { icon: Code2, text: 'Development' },
  { icon: Palette, text: 'Design' },
  { icon: Database, text: 'Database' },
  { icon: Globe, text: 'Deployment' },
  { icon: Shield, text: 'Security' }
]

export default function RevealBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <RevealBento items={revealItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}