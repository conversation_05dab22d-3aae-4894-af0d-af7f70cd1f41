'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const bentoComponents: Array<{ name: string; href: string }> = [
  { name: '<PERSON><PERSON>', href: '/components/bento/bento' },
  { name: '3D Bento', href: '/components/bento/3d-bento' },
  { name: 'Gallery Bento', href: '/components/bento/gallery-bento' },
  { name: 'Reveal Bento', href: '/components/bento/reveal-bento' },
  { name: 'Border Fill Bento', href: '/components/bento/border-fill-bento' },
  { name: 'Glowing <PERSON>to', href: '/components/bento/glowing-bento' },
  { name: 'Pattern Bento', href: '/components/bento/pattern-bento' },
  { name: 'Info Reveal Bento', href: '/components/bento/info-reveal-bento' },
  { name: 'Simple KPI Bento', href: '/components/bento/simple-kpi-bento' },
  { name: 'Simple Steps Bento', href: '/components/bento/simple-steps-bento' },
  { name: 'K<PERSON> Bento', href: '/components/bento/kpi-bento' },
  { name: 'Pricing Bento', href: '/components/bento/pricing-bento' }
];

export default function BentoPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {bentoComponents.map((component) => (
            <Link key={component.name} href={component.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}