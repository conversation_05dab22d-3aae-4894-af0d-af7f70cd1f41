'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import PricingBento from '@/components/bento/PricingBento'
import { PageNav } from '@/components/common/PageNav'
import { Package, Zap, Building2 } from 'lucide-react'

const pricingItems = [
  {
    badge: 'Standard',
    badgeIcon: Package,
    price: '$0/mth',
    subtitle: 'No charge, ever.',
    features: [
      'Claim-by-claim patent review module',
      'Claim text highlighting',
      'Up to 3 active reviews',
      'Basic email support',
    ]
  },
  {
    badge: 'Professional',
    badgeIcon: Zap,
    price: '$29/mth',
    subtitle: 'Per user, per month billed annualy.',
    features: [
      'Everything in Standard',
      'Unlimited active reviews',
      'Advanced analytics dashboard',
      'Priority email & chat support',
    ]
  },
  {
    badge: 'Enterprise',
    badgeIcon: Building2,
    price: '$99/mth',
    subtitle: 'For both in-house and law firm teams',
    features: [
      'Everything in Pro',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantee 99.9% uptime',
    ]
  }
]

export default function PricingBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <section className="min-h-screen flex items-center px-[var(--width-10)]">
        <PricingBento items={pricingItems} />
      </section>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">End</p>
      </div>
    </ReactLenis>
  )
}