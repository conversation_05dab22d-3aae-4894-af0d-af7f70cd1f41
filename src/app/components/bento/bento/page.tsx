'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import <PERSON><PERSON> from '@/components/bento/Bento'
import { PageNav } from '@/components/common/PageNav'

const bentoItems = [
  {
    title: 'Architecture & Development',
    description: 'Building scalable and maintainable application structures with modern design patterns and best practices.'
  },
  {
    title: 'Design Systems',
    description: 'Creating cohesive visual languages and component libraries.'
  },
  {
    title: 'Performance',
    description: 'Optimizing applications for speed and efficiency.'
  },
  {
    title: 'User Experience',
    description: 'Crafting intuitive interfaces that delight users and achieve business goals through research and testing.'
  }
]

export default function BentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <Bento items={bentoItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}