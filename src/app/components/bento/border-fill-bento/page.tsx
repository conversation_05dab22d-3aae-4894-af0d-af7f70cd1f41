'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import BorderFillBento from '@/components/bento/borderFillBento/BorderFillBento'
import { PageNav } from '@/components/common/PageNav'
import { Layers, Code2, Palette, Rocket, Shield, Zap } from 'lucide-react'

const borderFillItems = [
  {
    icon: Layers,
    title: 'Architecture',
    description: 'Building scalable and maintainable application structures with modern design patterns and best practices.'
  },
  {
    icon: Code2,
    title: 'Development',
    description: 'Creating robust features with clean code, comprehensive testing, and continuous integration workflows.'
  },
  {
    icon: Palette,
    title: 'Design',
    description: 'Crafting beautiful user interfaces with intuitive interactions and accessible experiences for all users.'
  },
  {
    icon: Rocket,
    title: 'Performance',
    description: 'Optimizing applications for speed and efficiency with code splitting, lazy loading, and caching strategies.'
  },
  {
    icon: Shield,
    title: 'Security',
    description: 'Implementing secure authentication, data encryption, and protection against common vulnerabilities.'
  },
  {
    icon: Zap,
    title: 'Innovation',
    description: 'Exploring cutting-edge technologies and methodologies to deliver next-generation digital experiences.'
  }
]

export default function BorderFillBentoPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <BorderFillBento items={borderFillItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}