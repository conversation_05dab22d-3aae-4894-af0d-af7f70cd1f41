'use client';

import Image from 'next/image';
import SplitNavbar from '@/components/navigation/splitNavbar/SplitNavbar';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';
import { NavItem } from '@/types/navigation';

export default function SplitNavbarPage() {
    const navItems: NavItem[] = [
        { name: 'Home', id: 'home' },
        { name: 'About', id: 'about' },
        { name: 'Services', id: 'services' },
        { name: 'Contact', id: 'contact' },
    ];

    return (
        <ReactLenis root>
            <PageNav position="bottom" />
            <SplitNavbar 
                logoSrc="/images/logo.svg"
                logoWidth={120}
                logoHeight={40}
                buttonText="Get Started"
                onButtonClick={() => console.log('Get Started clicked')}
                navItems={navItems}
                defaultSelectorValue="home"
                enableScrollDetection={true}
            />
            <section id="home" className="relative z-0 h-screen flex items-center justify-center">
                <p className="text-3xl">Home</p>
                <Image 
                    className="absolute top-0 left-0 w-full h-screen object-cover" 
                    src="/images/placeholder3.avif" 
                    alt=""
                    fill
                    priority
                />
            </section>
            <section id="about" className="h-screen flex items-center justify-center">
                <p className="text-3xl">About</p>
            </section>
            <section id="services" className="h-screen flex items-center justify-center">
                <p className="text-3xl">Services</p>
            </section>
            <section id="contact" className="h-screen flex items-center justify-center">
                <p className="text-3xl">Contact</p>
            </section>
        </ReactLenis>
    );
}