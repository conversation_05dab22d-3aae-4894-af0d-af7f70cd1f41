'use client';

import FloatingNavbar from '@/components/navigation/floatingNavbar/FloatingNavbar';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';
import { NavItem } from '@/types/navigation';

export default function FloatingNavbarPage() {
    const navItems: NavItem[] = [
        { name: 'Home', id: 'home' },
        { name: 'About', id: 'about' },
        { name: 'Solutions', id: 'solutions' },
        { name: 'Community', id: 'community' },
    ];
    
    return (
        <ReactLenis root>
            <PageNav position="bottom" />
            <FloatingNavbar 
                navItems={navItems}
                logoSrc="/images/logo.svg"
                logoWidth={120}
                logoHeight={40}
                buttonText="Join Now"
                onButtonClick={() => console.log('Join Now clicked')}
            />
            <section id="home" className="h-screen flex items-center justify-center text-black">
                <p>Home</p>
            </section>
            <section id="about" className="h-screen flex items-center justify-center text-black">
                <p>About</p>
            </section>
            <section id="solutions" className="h-screen flex items-center justify-center text-black">
                <p>Solutions</p>
            </section>
            <section id="community" className="h-screen flex items-center justify-center text-black">
                <p>Community</p>
            </section>
        </ReactLenis>
    );
}