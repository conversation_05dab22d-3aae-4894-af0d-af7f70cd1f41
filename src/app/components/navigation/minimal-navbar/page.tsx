'use client';

import MinimalNavbar from '@/components/navigation/MinimalNavbar';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';

export default function MinimalNavbarPage() {
    return (
        <ReactLenis root>
            <PageNav position="bottom" />
            <MinimalNavbar
                logoSrc="/images/logo.svg"
                logoWidth={120}
                logoHeight={40}
                buttonText="Get Started"
                onButtonClick={() => console.log('Get Started clicked')}
            />
            <section id="home" className="h-screen flex items-center justify-center">
                <p className="text-3xl">Home</p>
            </section>
            <section id="about" className="h-screen flex items-center justify-center">
                <p className="text-3xl">About</p>
            </section>
            <section id="solutions" className="h-screen flex items-center justify-center">
                <p className="text-3xl">Solutions</p>
            </section>
            <section id="community" className="h-screen flex items-center justify-center">
                <p className="text-3xl">Community</p>
            </section>
        </ReactLenis>
    );
}