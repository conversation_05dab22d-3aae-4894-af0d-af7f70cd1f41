'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const buttons = [
  { name: 'Rotating Icon Button', href: '/components/buttons/rotating-icon-button' },
  { name: '<PERSON><PERSON> Button', href: '/components/buttons/stagger-button' },
  { name: 'Shift Button', href: '/components/buttons/shift-button' },
  { name: 'Directional Hover Button', href: '/components/buttons/directional-hover-button' },
  { name: 'Bubble Button', href: '/components/buttons/bubble-button' },
  { name: 'Underline Button', href: '/components/buttons/underline-button' },
  { name: 'Arrow Button', href: '/components/buttons/arrow-button' },
  { name: 'Expanding Button', href: '/components/buttons/expanding-button' },
  { name: 'Magnetic Button', href: '/components/buttons/magnetic-button' },
  { name: 'Slide Button', href: '/components/buttons/slide-button' },
  { name: 'Moving Border Button', href: '/components/buttons/moving-border-button' },
  { name: 'Selector Button', href: '/components/buttons/selector-button' },
  { name: 'Pushable Button', href: '/components/buttons/pushable-button' }
];

export default function ButtonsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {buttons.map((button) => (
            <Link key={button.name} href={button.href}
            >
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{button.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}