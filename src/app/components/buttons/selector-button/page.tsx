'use client';

import React, { useState } from 'react';
import SelectorButton from '@/components/buttons/SelectorButton';
import { PageNav } from '@/components/common/PageNav';

export default function SelectorButtonPage() {
    const [selectedValue, setSelectedValue] = useState('option1');

    const basicOptions = [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
    ];

    return (
        <section className="min-h-screen py-[var(--width-10)]">
            <PageNav />
            <div className="w-full px-[var(--width-10)] flex items-center justify-center">
                <SelectorButton
                    options={basicOptions}
                    activeValue={selectedValue}
                    onValueChange={setSelectedValue}
                />
            </div>
        </section>
    );
}