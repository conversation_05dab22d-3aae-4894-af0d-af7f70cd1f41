'use client';

import React from 'react';
import PushableButton from '@/components/buttons/PushableButton';
import { PageNav } from '@/components/common/PageNav';

export default function PushableButtonPage() {
    return (
        <section className="relative z-10 min-h-screen flex flex-col justify-center px-[var(--width-10)]">
            <PageNav />
            <div className="w-full flex justify-center">
                <PushableButton 
                    text="Push me"
                    onClick={() => console.log('Pushed!')}
                />
            </div>
        </section>
    );
}