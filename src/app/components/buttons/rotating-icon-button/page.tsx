'use client';

import React from 'react';
import RotatingIconButton from '@/components/buttons/rotatingIconButton/RotatingIconButton';
import { PageNav } from '@/components/common/PageNav';

export default function RotatingIconButtonPage() {
  return (
    <div className="min-h-screen p-page-padding flex items-center justify-center">
      <PageNav />
      <RotatingIconButton
        text="Button with Icon"
        onClick={() => console.log('Button clicked!')}
      />
    </div>
  );
}