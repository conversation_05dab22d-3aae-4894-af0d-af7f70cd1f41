'use client';

import React from 'react';
import StandardTextbox from '@/components/textbox/StandardTextbox';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';
import HighlightText from '@/components/text/HighlightText';
import RotateText from '@/components/text/RotateText';
import SlideText from '@/components/text/SlideText';
import BlurText from '@/components/text/BlurText';

const TITLE_TEXT = "Secure by design";
const DESCRIPTION_TEXT = "Bank confidently with enhanced FDIC coverage up to hundreds of millions, granular user controls, advanced authentication, and federally regulated bank partners.";

export default function StandardTextboxPage() {
  return (
    <ReactLenis root>

      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <StandardTextbox
          title={<h1>{TITLE_TEXT}</h1>}
          description={<p>{DESCRIPTION_TEXT}</p>}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <StandardTextbox
          title={<HighlightText text={TITLE_TEXT} variant="trigger" />}
          description={<HighlightText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <StandardTextbox
          title={<RotateText text={TITLE_TEXT} variant="trigger" />}
          description={<RotateText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <StandardTextbox
          title={<SlideText text={TITLE_TEXT} variant="trigger" />}
          description={<SlideText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <StandardTextbox
          title={<BlurText text={TITLE_TEXT} variant="trigger" />}
          description={<BlurText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}