"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const textboxes = [
  { name: "Standard Textbox", href: "/components/textbox/standard-textbox" },
  {
    name: "Horizontal Textbox",
    href: "/components/textbox/horizontal-textbox",
  },
  { name: "Content Textbox", href: "/components/textbox/content-textbox" },
  { name: "Tagged Textbox", href: "/components/textbox/tagged-textbox" },
  { name: "Inline Textbox", href: "/components/textbox/inline-textbox" },
];

export default function TextboxPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {textboxes.map((textbox) => (
            <Link key={textbox.name} href={textbox.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{textbox.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
