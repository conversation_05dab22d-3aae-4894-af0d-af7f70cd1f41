'use client';

import React from 'react';
import InlineTextbox from '@/components/textbox/InlineTextbox';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';
import HighlightText from '@/components/text/HighlightText';
import RotateText from '@/components/text/RotateText';
import SlideText from '@/components/text/SlideText';
import BlurText from '@/components/text/BlurText';

const TITLE_TEXT = "Why us";
const DESCRIPTION_TEXT = "At Dialedweb, we embody the startup mindset. Dynamic, innovative, and hungry to make a difference. We don't just create amazing works, we partner with our clients to revolutionize their industries through groundbreaking digital experiences. From redefining brand engagement to boosting conversions, every project we take on is an opportunity to challenge the norm, deliver excellence, and leave an impact.";

export default function InlineTextboxPage() {
  return (
    <ReactLenis root>

      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <InlineTextbox
          title={<span>{TITLE_TEXT}</span>}
          description={<span>{DESCRIPTION_TEXT}</span>}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <InlineTextbox
          title={<HighlightText text={TITLE_TEXT} variant="trigger" />}
          description={<HighlightText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <InlineTextbox
          title={<RotateText text={TITLE_TEXT} variant="trigger" />}
          description={<RotateText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <InlineTextbox
          title={<SlideText text={TITLE_TEXT} variant="trigger" />}
          description={<SlideText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <InlineTextbox
          title={<BlurText text={TITLE_TEXT} variant="trigger" />}
          description={<BlurText text={DESCRIPTION_TEXT} variant="words-trigger" />}
        />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}