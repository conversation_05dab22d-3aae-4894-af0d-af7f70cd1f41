import Link from 'next/link';

export default function ComponentsPage() {
  const categories = [
    {
      title: "Carousels",
      href: "/components/carousels"
    },
    {
      title: "Buttons",
      href: "/components/buttons"
    },
    {
      title: "Text",
      href: "/components/text"
    },
    {
      title: "Timeline",
      href: "/components/timeline"
    },
    {
      title: "Bento",
      href: "/components/bento"
    },
    {
      title: "Accordions",
      href: "/components/accordions"
    },
    {
      title: "Textbox",
      href: "/components/textbox"
    },
    {
      title: "Navigation",
      href: "/components/navigation"
    },
    {
      title: "Footer",
      href: "/components/footer"
    },
    {
      title: "Sections",
      href: "/components/sections"
    }
  ];

  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {categories.map((category) => (
            <Link key={category.href} href={category.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{category.title}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}