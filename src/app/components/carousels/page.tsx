import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

export default function CarouselsPage() {
  const carousels = [
    {
      title: "Carousel",
      href: "/components/carousels/carousel"
    },
    {
      title: "Progress Carousel",
      href: "/components/carousels/progress-carousel"
    },
    {
      title: "Stack Carousel",
      href: "/components/carousels/stack-carousel"
    },
    {
      title: "Infinite Carousel",
      href: "/components/carousels/infinite-carousel"
    },
    {
      title: "Auto Carousel",
      href: "/components/carousels/auto-carousel"
    },
    {
      title: "Fullscreen Carousel",
      href: "/components/carousels/fullscreen-carousel"
    },
    {
      title: "Draggable Carousel",
      href: "/components/carousels/draggable-carousel"
    },
    {
      title: "Autoscroll Carousel",
      href: "/components/carousels/autoscroll-carousel"
    }
  ];

  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {carousels.map((carousel) => (
            <Link key={carousel.href} href={carousel.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{carousel.title}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}