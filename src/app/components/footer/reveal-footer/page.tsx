'use client';

import React from 'react';
import RevealFooter from '@/components/footer/RevealFooter';
import { PageNav } from '@/components/common/PageNav';
import { ReactLenis } from 'lenis/react';

export default function RevealFooterPage() {
    const footerColumns = [
        {
            title: 'Company',
            items: [
                { label: 'About', onClick: () => console.log('About clicked') },
                { label: 'Blog', onClick: () => console.log('Blog clicked') },
                { label: 'Careers', onClick: () => console.log('Careers clicked') },
                { label: 'Contact', onClick: () => console.log('Contact clicked') },
            ]
        },
        {
            title: 'Resources',
            items: [
                { label: 'Community', onClick: () => console.log('Community clicked') },
                { label: 'Support', onClick: () => console.log('Support clicked') },
                { label: 'Status', onClick: () => console.log('Status clicked') },
                { label: 'Partners', onClick: () => console.log('Partners clicked') },
            ]
        },
        {
            title: 'Legal',
            items: [
                { label: 'Terms', onClick: () => console.log('Terms clicked') },
                { label: 'Privacy', onClick: () => console.log('Privacy clicked') },
                { label: 'Cookies', onClick: () => console.log('Cookies clicked') },
                { label: 'License', onClick: () => console.log('License clicked') },
            ]
        }
    ];

    return (
        <ReactLenis root>
            <PageNav />
            <div className="relative z-10 h-screen flex items-center justify-center">
                <p className="text-3xl">Scroll down to see the reveal footer</p>
            </div>
            <RevealFooter
                logoSrc="/images/logowhite.svg"
                logoWidth={120}
                logoHeight={40}
                columns={footerColumns}
                copyrightText="© 2025 | Webild"
                onPrivacyClick={() => console.log('Privacy Policy clicked')}
            />
        </ReactLenis>
    );
}