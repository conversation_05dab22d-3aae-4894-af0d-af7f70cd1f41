'use client';

import React from 'react';
import GradientFooter from '@/components/footer/GradientFooter';
import { PageNav } from '@/components/common/PageNav';
import ReactLenis from 'lenis/react';

export default function GradientFooterPage() {
    const footerItems = [
        { label: 'About', onClick: () => console.log('About clicked') },
        { label: 'Services', onClick: () => console.log('Services clicked') },
        { label: 'Portfolio', onClick: () => console.log('Portfolio clicked') },
        { label: 'Contact', onClick: () => console.log('Contact clicked') },
        { label: 'Privacy', onClick: () => console.log('Privacy clicked') },
        { label: 'Terms', onClick: () => console.log('Terms clicked') },
    ];

    return (
        <ReactLenis root>
            <PageNav />
            <div className="relative z-10 h-screen flex items-center justify-center bg-black">
                <p className="text-3xl text-white">Scroll down to see the logo footer</p>
            </div>
            <GradientFooter
                logoSrc="/images/logowhite.svg"
                logoAlt="Company Logo"
                items={footerItems}
            />

        </ReactLenis>
    );
}