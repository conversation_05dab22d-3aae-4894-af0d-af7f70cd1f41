'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const footers: Array<{ name: string; href: string }> = [
  {
    name: 'Simple Footer',
    href: '/components/footer/simple-footer'
  },
  {
    name: 'Reveal Footer',
    href: '/components/footer/reveal-footer'
  },
  {
    name: 'Logo Footer',
    href: '/components/footer/logo-footer'
  },
  {
    name: 'Gradient Footer',
    href: '/components/footer/gradient-footer'
  }
];

export default function FooterPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {footers.map((footer) => (
            <Link key={footer.name} href={footer.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{footer.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}