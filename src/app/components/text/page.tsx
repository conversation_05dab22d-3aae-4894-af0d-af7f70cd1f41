'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const textComponents = [
  { name: 'Gradient Text', href: '/components/text/gradient-text' },
  { name: 'Hover Gradient Text', href: '/components/text/hover-gradient-text' },
  { name: 'Highlight Text', href: '/components/text/highlight-text' },
  { name: 'Rotate Text', href: '/components/text/rotate-text' },
  { name: 'Slide Text', href: '/components/text/slide-text' },
  { name: 'Blur Text', href: '/components/text/blur-text' },
  { name: 'Scale Text', href: '/components/text/scale-text' },
  { name: 'Expand Text', href: '/components/text/expand-text' },
  { name: 'Flip Text', href: '/components/text/flip-text' },
  { name: 'Parallax Text', href: '/components/text/parallax-text' },
  { name: 'Horizontal Text', href: '/components/text/horizontal-text' },
  { name: 'Mask Text', href: '/components/text/mask-text' },
  { name: 'Animated Number Text', href: '/components/text/animated-number-text' },
  { name: 'Cube Text', href: '/components/text/cube-text' },
  { name: 'Wave Text', href: '/components/text/wave-text' },
  { name: 'Color Reveal Text', href: '/components/text/color-reveal-text' },
  { name: 'Retro Text', href: '/components/text/retro-text' }
];

export default function TextPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {textComponents.map((component) => (
            <Link key={component.name} href={component.href}
            >
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}