'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import ParallaxText from '@/components/text/ParallaxText';
import { PageNav } from '@/components/common/PageNav';

export default function ParallaxTextPage() {
  return (
    <ReactLenis root>
      <PageNav />
      
      <div className="min-h-screen flex items-center justify-center">
        <ParallaxText
          text="Infinite Scroll"
          baseVelocity={-2}
          textClassName="text-9xl font-semibold font-playfair"
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}