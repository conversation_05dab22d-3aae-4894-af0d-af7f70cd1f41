'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import ColorRevealText from '@/components/text/ColorRevealText';
import { PageNav } from '@/components/common/PageNav';

export default function ColorRevealTextPage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="min-h-screen flex items-center justify-center text-center">
        <ColorRevealText
          text="Welcome to the future"
          className="text-5xl"
          scrollTrigger={true}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <ColorRevealText
          text="Transform your digital presence with cutting-edge solutions that drive innovation and deliver exceptional user experiences across all platforms and devices. We leverage the latest technologies and industry best practices to create scalable, performant applications that not only meet but exceed your business objectives, ensuring long-term success in an ever-evolving digital landscape where adaptability and excellence are paramount"
          className="text-xl max-w-4xl"
          scrollTrigger={true}
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}