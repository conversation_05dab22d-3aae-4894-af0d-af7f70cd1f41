'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import CubeText from '@/components/text/cubeText/CubeText';
import { PageNav } from '@/components/common/PageNav';

export default function CubeTextPage() {
  return (
    <ReactLenis root>
      <PageNav />
      
      <div className="min-h-screen flex items-center justify-center text-center">
        <CubeText
          items={['Brands.', 'Shows.', 'Content.', 'Trends.']}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <CubeText
          items={['Design', 'Develop', 'Deploy', 'Deliver']}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <CubeText
          items={['Create', 'Innovate', 'Inspire', 'Transform']}
          duration={8}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <CubeText
          items={['Fast', 'Secure', 'Scalable', 'Simple']}
          duration={15}
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}