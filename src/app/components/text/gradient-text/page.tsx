'use client';

import React from 'react';
import { React<PERSON>enis } from 'lenis/react';
import GradientText from '@/components/text/GradientText';
import { PageNav } from '@/components/common/PageNav';

export default function GradientTextPage() {
  return (
    <ReactLenis root>
      <PageNav />
      
      <div className="min-h-screen flex items-center justify-center">
        <GradientText
          text="Beautiful Gradient Text"
          className="text-6xl font-semibold text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <GradientText
          text="Create stunning visual effects with radial gradients that capture attention and enhance your design."
          className="text-4xl text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <GradientText
          text="This gradient text component uses a radial gradient with multiple color stops to create a vibrant, eye-catching effect that draws attention to your important messages."
          className="text-3xl text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <GradientText
          text="Perfect for headlines, subheadings, and any text that needs to stand out from the crowd."
          className="text-4xl text-center max-w-4xl"
        />
      </div>
    </ReactLenis>
  );
}