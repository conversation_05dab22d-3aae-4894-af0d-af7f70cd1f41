'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const timelineComponents = [
  { name: 'Timeline', href: '/components/timeline/timeline' },
  { name: 'Popup Timeline', href: '/components/timeline/popup-timeline' },
  { name: 'Horizontal Timeline', href: '/components/timeline/horizontal-timeline' },
  { name: 'Stack Timeline', href: '/components/timeline/stack-timeline' },
  { name: '3D Stack Timeline', href: '/components/timeline/3d-stack-timeline' },
  { name: 'Process Timeline', href: '/components/timeline/process-timeline' },
  { name: 'Year Timeline', href: '/components/timeline/year-timeline' },
  { name: 'Phone Timeline', href: '/components/timeline/phone-timeline' }
];

export default function TimelinePage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {timelineComponents.map((component) => (
            <Link key={component.name} href={component.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}