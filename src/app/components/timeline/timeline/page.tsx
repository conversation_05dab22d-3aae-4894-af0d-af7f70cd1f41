'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import Timeline from '@/components/timeline/Timeline';
import { PageNav } from '@/components/common/PageNav';

const timelineData = [
  {
    title: 'Phase 1',
    description: 'Initial research and planning phase for the project development',
    image: '/images/placeholdersmall1.avif'
  },
  {
    title: 'Phase 2',
    description: 'Design and prototyping with user feedback integration',
    image: '/images/placeholdersmall2.avif'
  },
  {
    title: 'Phase 3',
    description: 'Development and implementation of core features',
    image: '/images/placeholdersmall3.avif'
  },
  {
    title: 'Phase 4',
    description: 'Testing, optimization and final deployment',
    image: '/images/placeholdersmall4.avif'
  }
];

export default function TimelinePage() {
  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <Timeline
          items={timelineData}
          title="Project Timeline"
        />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}