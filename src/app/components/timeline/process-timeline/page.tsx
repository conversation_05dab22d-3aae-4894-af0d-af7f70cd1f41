'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import ProcessTimeline from '@/components/timeline/ProcessTimeline';
import { PageNav } from '@/components/common/PageNav';
import type { ProcessTimelineItem } from '@/types/timeline';
import { Award, BarChart2, Eye, Lightbulb, RefreshCw, Target, Zap } from 'lucide-react';

const processItems: ProcessTimelineItem[] = [
  {
    id: "01",
    title: "Research & Discovery Phase",
    description: "Initial research and planning phase for the project development including market analysis, user interviews, competitive research, and technical feasibility studies to ensure project success",
    image: "/images/placeholder1.avif",
    items: [
      { icon: Target, text: "Market analysis & user research" },
      { icon: BarChart2, text: "Competitive analysis" },
      { icon: Lightbulb, text: "Technical feasibility studies" }
    ],
    reverse: false
  },
  {
    id: "02",
    title: "Design & Prototyping Phase",
    description: "Design and prototyping with user feedback integration through iterative design sprints, wireframing, high-fidelity mockups, and continuous user testing to validate design decisions",
    image: "/images/placeholder2.avif",
    items: [
      { icon: Zap, text: "Iterative design sprints" },
      { icon: Eye, text: "High-fidelity mockups" },
      { icon: RefreshCw, text: "User testing & validation" }
    ],
    reverse: true
  },
  {
    id: "03",
    title: "Development & Implementation",
    description: "Development and implementation of core features using agile methodologies, continuous integration, code reviews, and quality assurance to deliver a robust and scalable solution",
    image: "/images/placeholder3.avif",
    items: [
      { icon: RefreshCw, text: "Agile development" },
      { icon: Award, text: "Code reviews & CI/CD" },
      { icon: Target, text: "Quality assurance" }
    ],
    reverse: false
  }
];

export default function ProcessTimelinePage() {
  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <ProcessTimeline items={processItems} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}